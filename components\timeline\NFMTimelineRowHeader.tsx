/**
 * Timeline row header component for modality information and navigation
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronLeft, ChevronRight, Eye, EyeOff } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { MultiSelect } from '@/components/ui_custom/multi-select';
import { Doc } from '@/convex/_generated/dataModel';
import { TimelineEvent } from './interface/timeline';
import { useIsMobile } from '@/hooks/use-mobile';

export interface NFMTimelineRowHeaderProps {
  modality: Doc<"modalityConfigs">;
  events: TimelineEvent[];
  currentTime: number;
  isVisible?: boolean;
  onToggleVisibility?: (modalityId: string, visible: boolean) => void;
  onNavigateToEvent?: (time: number) => void;
  className?: string;
}

export function NFMTimelineRowHeader({
  modality,
  events,
  currentTime,
  isVisible = true,
  onToggleVisibility,
  onNavigateToEvent,
  className
}: NFMTimelineRowHeaderProps) {
  const [isHovered, setIsHovered] = useState(false);
  const isMobile = useIsMobile();

  // Get events for this modality sorted by time
  const modalityEvents = events
    .filter(event => event.modalityId === modality._id)
    .sort((a, b) => a.start - b.start);

  // Find previous and next events relative to current time
  const pastEvents = modalityEvents.filter(event => event.end < currentTime);
  const futureEvents = modalityEvents.filter(event => event.start > currentTime);
  
  const prevEvent = pastEvents.length > 0 ? pastEvents[pastEvents.length - 1] : null;
  const nextEvent = futureEvents.length > 0 ? futureEvents[0] : null;

  const handlePrevEvent = () => {
    if (prevEvent && onNavigateToEvent) {
      onNavigateToEvent(prevEvent.start);
    }
  };

  const handleNextEvent = () => {
    if (nextEvent && onNavigateToEvent) {
      onNavigateToEvent(nextEvent.start);
    }
  };

  const handleToggleVisibility = () => {
    if (onToggleVisibility) {
      onToggleVisibility(modality._id, !isVisible);
    }
  };

  return (
    <div
      className={cn(
        'timeline-row-header',
        'flex items-center justify-between px-3 py-2 border-r border-gray-200 dark:border-gray-700',
        'bg-gray-50 dark:bg-gray-800 transition-all duration-200',
         isMobile? 'min-w-[100px] max-w-[100px]' :'min-w-[150px] max-w-[150px]', // Fixed width
        'select-none', // Prevent text selection
        {
          'bg-gray-100 dark:bg-gray-700': isHovered,
          'opacity-50': !isVisible
        },
        className
      )}
      style={{
        height: '40px', // Match timeline row height
        borderLeftColor: modality.colorCode,
        borderLeftWidth: '4px',
        borderLeftStyle: 'solid'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Left side - Modality info */}
      <div className="flex items-center gap-2 flex-1 min-w-0">
        {/* Modality color indicator */}
        <div
          className="w-3 h-3 rounded-full flex-shrink-0"
          style={{ backgroundColor: modality.colorCode }}
        />
        
        {/* Modality name */}
        <div className="flex flex-col min-w-0 flex-1">
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
            {modality.name}
          </span>
          {modalityEvents.length > 0 && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {modalityEvents.length} event{modalityEvents.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>
      </div>

      {/* Right side - Navigation and visibility controls */}
      <div className="flex items-center gap-1 flex-shrink-0">
        {/* Navigation buttons (shown on hover) */}
        {isHovered && modalityEvents.length > 0 && (
          <>
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrevEvent}
              disabled={!prevEvent}
              title={prevEvent ? `Previous event at ${prevEvent.start.toFixed(1)}s` : 'No previous events'}
              className="h-6 w-6 p-0"
            >
              <ChevronLeft className="h-3 w-3" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNextEvent}
              disabled={!nextEvent}
              title={nextEvent ? `Next event at ${nextEvent.start.toFixed(1)}s` : 'No upcoming events'}
              className="h-6 w-6 p-0"
            >
              <ChevronRight className="h-3 w-3" />
            </Button>
          </>
        )}

        {/* Visibility toggle 
        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggleVisibility}
          title={isVisible ? 'Hide modality' : 'Show modality'}
          className="h-6 w-6 p-0"
        >
          {isVisible ? (
            <Eye className="h-3 w-3" />
          ) : (
            <EyeOff className="h-3 w-3" />
          )}
        </Button>*/}
      </div>
    </div>
  );
}

/**
 * Container component for all timeline row headers
 */
export interface NFMTimelineRowHeadersProps {
  modalities: Doc<"modalityConfigs">[];
  events: Doc<"monitoringEvents">[];
  currentTime: number;
  visibleModalities?: Doc<"modalityConfigs">[];
  onToggleVisibility?: (modalityId: string, visible: boolean) => void;
  onNavigateToEvent?: (time: number) => void;
  onModalityVisibilityChange?: (modalityIds: string[]) => void;
  className?: string;
}

export function NFMTimelineRowHeaders({
  modalities,
  events,
  currentTime,
  visibleModalities = [],
  onToggleVisibility,
  onNavigateToEvent,
  onModalityVisibilityChange,
  className
}: NFMTimelineRowHeadersProps) {
  // Convert events to timeline format for compatibility
  const timelineEvents: TimelineEvent[] = events.map(event => ({
    ...event,
    id: event._id,
    start: event.startTime,
    end: event.endTime || event.startTime + 1,
    effectId: `modality-${event.modalityId}`,
    selected: false,
    flexible: true,
    movable: true,
    disable: false,
    minStart: 0,
    maxEnd: Number.MAX_VALUE,
    modalityName: modalities.find(m => m._id === event.modalityId)?.name || '',
    modalityColor: modalities.find(m => m._id === event.modalityId)?.colorCode || '#6b7280'
  }));

  return (
    <div className={cn('timeline-row-headers', 'flex flex-col', className)}>
      {/* MultiSelect for modality visibility - positioned above row headers */}
      <div className="p-2 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
        <MultiSelect
          minimized
          options={modalities.map(modality => ({
            label: modality.name,
            value: modality._id,
            icon: () => (
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: modality.colorCode }}
              />
            )
          }))}
          onValueChange={onModalityVisibilityChange || (() => {})}
          defaultValue={visibleModalities.map(m => m._id)}
          placeholder="Select modalities..."
          variant="default"
          animation={0}
          maxCount={0}
          className="w-full text-xs min-h-7 h-7 "
        />
         
      </div>

      {/* Row headers */}
      {visibleModalities
        .map(modality => (
          <NFMTimelineRowHeader
            key={modality._id}
            modality={modality}
            events={timelineEvents}
            currentTime={currentTime}
            isVisible={true} //{visibleModalities.length === 0 || visibleModalities.includes(modality._id)}
            onToggleVisibility={onToggleVisibility}
            onNavigateToEvent={onNavigateToEvent}
          />
        ))}
    </div>
  );
}

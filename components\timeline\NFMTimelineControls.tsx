/**
 * NFM Timeline Controls - Control panel for the timeline editor
 */

import React, { useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { InteractionMode, NFMTimelineControlsProps } from '@/types/timelineEditor';
import {
  Play,
  Pause,
  Plus,
  Minus,
  SkipForward,
  SkipBack,
  Edit3,
  Lock
} from 'lucide-react';
import PlaybackSpeedButton from '../ui_custom/playback-speed-button';

export function NFMTimelineControls({
  currentTime,
  duration,
  isPlaying,
  onPlay,
  onPause,
  onZoom,
  onPrevEvent,
  onNextEvent,
  playbackRate = 1,
  onPlaybackRateChange,
  onScrollToTime,
  interactionMode = InteractionMode.VIEW, // Default to editing disabled
  setInteractionMode,
  events,
  modalities,
  className
}: NFMTimelineControlsProps) {
  // Handle play/pause toggle
  const handlePlayPause = useCallback(() => {
    if (isPlaying) {
      onPause?.();
    } else {
      onPlay?.();
    }
  }, [isPlaying, onPlay, onPause]);

  // Enhanced event navigation with auto-scroll
  const sortedEvents = useMemo(() => {
    if (!events || events.length === 0) return [];
    return [...events].sort((a, b) => a.startTime - b.startTime);
  }, [events]);

  const handlePrevEventWithScroll = useCallback(() => {
    if (sortedEvents.length === 0) return;

    // Find the previous event before current time
    let prevEvent = null;
    for (let i = sortedEvents.length - 1; i >= 0; i--) {
      if (sortedEvents[i].startTime < currentTime) {
        prevEvent = sortedEvents[i];
        break;
      }
    }

    // If no previous event found, go to the last event
    if (!prevEvent && sortedEvents.length > 0) {
      prevEvent = sortedEvents[sortedEvents.length - 1];
    }

    if (prevEvent) {
      onPrevEvent?.();
      // Auto-scroll to center the event in viewport
      onScrollToTime?.(prevEvent.startTime);
    }
  }, [sortedEvents, currentTime, onPrevEvent, onScrollToTime]);

  const handleNextEventWithScroll = useCallback(() => {
    if (sortedEvents.length === 0) return;

    // Find the next event after current time
    const nextEvent = sortedEvents.find(event => event.startTime > currentTime);

    if (nextEvent) {
      onNextEvent?.();
      // Auto-scroll to center the event in viewport
      onScrollToTime?.(nextEvent.startTime);
    }
  }, [sortedEvents, currentTime, onNextEvent, onScrollToTime]);

  // Check if navigation buttons should be disabled
  const hasPrevEvent = useMemo(() => {
    return sortedEvents.some(event => event.startTime < currentTime);
  }, [sortedEvents, currentTime]);

  const hasNextEvent = useMemo(() => {
    return sortedEvents.some(event => event.startTime > currentTime);
  }, [sortedEvents, currentTime]);

  // Handle playback speed change
  const handlePlaybackSpeedChange = useCallback((speed: number) => {
    onPlaybackRateChange?.(speed);
  }, [onPlaybackRateChange]);
  
  return (
    // Enhanced centered controls with proper event navigation and playback speed
    <div className={cn(
      'flex items-center justify-center gap-2 p-3 bg-background border-b border-gray-200 dark:border-gray-700',
      className
    )}>
      {/* Previous Event */}
      <Button
        variant="outline"
        size="sm"
        onClick={handlePrevEventWithScroll}
        disabled={!hasPrevEvent}
        title={hasPrevEvent ? "Previous Event" : "No previous events"}
        className="h-8 w-8 p-0"
      >
        <SkipBack className="h-4 w-4" />
      </Button>

      {/* Play/Pause */}
      <Button
        variant={isPlaying ? "default" : "outline"}
        size="sm"
        onClick={handlePlayPause}
        title={isPlaying ? "Pause" : "Play"}
        className="h-8 w-8 p-0"
      >
        {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
      </Button>

      {/* Next Event */}
      <Button
        variant="outline"
        size="sm"
        onClick={handleNextEventWithScroll}
        disabled={!hasNextEvent}
        title={hasNextEvent ? "Next Event" : "No upcoming events"}
        className="h-8 w-8 p-0"
      >
        <SkipForward className="h-4 w-4" />
      </Button>

      {/* Divider */}
      <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

      {/* Zoom Out (Fixed: Minus button now zooms OUT) */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => onZoom?.(1.2)}
        title="Zoom Out (-)"
        className="h-8 w-8 p-0"
      >
        <Minus className="h-4 w-4" />
      </Button>

      {/* Zoom In (Fixed: Plus button now zooms IN) */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => onZoom?.(0.83)}
        title="Zoom In (+)"
        className="h-8 w-8 p-0"
      >
        <Plus className="h-4 w-4" />
      </Button>

      {/* Divider */}
      <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

      {/* Playback Speed Control */}
      <PlaybackSpeedButton
        startSpeed={playbackRate}
        onSpeedChange={handlePlaybackSpeedChange}
        className="h-8"
      />

      {/* Divider */}
      <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

      {/* Allow Editing Toggle */}
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1">
          {interactionMode === InteractionMode.EDIT ? (
            <Edit3 className="h-4 w-4 text-blue-600" />
          ) : (
            <Lock className="h-4 w-4 text-gray-500" />
          )}
        </div>
        <Switch
          checked={interactionMode === InteractionMode.EDIT}
          onCheckedChange={(checked) => setInteractionMode?.(checked ? InteractionMode.EDIT : InteractionMode.VIEW)}
          aria-label="Allow editing"
        />
        <Label
          htmlFor="allow-editing"
          className="text-sm font-medium cursor-pointer"
          onClick={() => setInteractionMode?.(interactionMode === InteractionMode.EDIT ? InteractionMode.VIEW : InteractionMode.EDIT)}
        >
          {interactionMode === InteractionMode.EDIT ? 'Editing' : 'Locked'}
        </Label>
      </div>
    </div>
  );
}


